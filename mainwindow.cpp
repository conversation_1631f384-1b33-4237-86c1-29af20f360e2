#include "mainwindow.h"
#include "ui_mainwindow.h"

using namespace std;

MainWindow::MainWindow(QWidget *parent) : QMainWindow(parent) , ui(new Ui::MainWindow)
{
    ui->setupUi(this);
    checkPlatformSupport();
    setupDVD();
    QTimer *myTimer = new QTimer();
    myTimer->setSingleShot(false);
    myTimer->start(1); // 約60FPSで更新（16ms間隔）
    connect(myTimer, &QTimer::timeout, this, &MainWindow::moveDVD);
}

MainWindow::~MainWindow()
{
    delete ui;
}

void MainWindow::setupDVD()
{
   vert = UP;
   hor = LEFT;
   QRect screenGeometry = screen->geometry();
   // シード値を設定
   srand(static_cast<unsigned int>(time(nullptr)));
   iconX = rand() % (screenGeometry.width() - width()) + 1;
   iconY = rand() % (screenGeometry.height() - height()) + 1;
}

void MainWindow::moveDVD()
{
   if (!dvd || !canMoveWindow) return;
   QRect screenGeometry = screen->geometry();
   
   // 境界判定を修正
   if (iconX <= 0 || iconX >= screenGeometry.width() - width()) {
       hor = (hor == LEFT) ? RIGHT : LEFT;
   }
   if (iconY <= 0 || iconY >= screenGeometry.height() - height()) {
       vert = (vert == UP) ? DOWN : UP;
   }

   // 位置を更新
   (vert == UP) ? iconY-- : iconY++;
   (hor == LEFT) ? iconX-- : iconX++;

   move(iconX, iconY);

   lastVert = vert;
   lastHor = hor;
}


void MainWindow::on_upButton_clicked()
{
    if (!canMoveWindow) {
        QMessageBox::information(this, "Platform Limitation", 
            "Window positioning is not supported on this platform (Wayland)");
        return;
    }
    QRect r = frameGeometry();
    this->move((r.x()), (r.y() - pixel));
}

void MainWindow::on_downButton_clicked()
{
    if (!canMoveWindow) {
        QMessageBox::information(this, "Platform Limitation", 
            "Window positioning is not supported on this platform (Wayland)");
        return;
    }
    QRect r = frameGeometry();
    this->move((r.x()), (r.y() + pixel));
}

void MainWindow::on_leftButton_clicked()
{
    if (!canMoveWindow) {
        QMessageBox::information(this, "Platform Limitation", 
            "Window positioning is not supported on this platform (Wayland)");
        return;
    }
    QRect r = frameGeometry();
    this->move((r.x() - pixel), (r.y()));
}

void MainWindow::on_rightButton_clicked()
{
    if (!canMoveWindow) {
        QMessageBox::information(this, "Platform Limitation", 
            "Window positioning is not supported on this platform (Wayland)");
        return;
    }
    QRect r = frameGeometry();
    this->move((r.x() + pixel), (r.y()));
}

void MainWindow::on_randomButton_clicked()
{
    if (!canMoveWindow) {
        QMessageBox::information(this, "Platform Limitation", 
            "Window positioning is not supported on this platform (Wayland)");
        return;
    }
    
    std::random_device rd;
    std::mt19937 gen(rd());
    QRect screenGeometry = screen->geometry();
    
    // ウィンドウサイズを考慮した範囲を設定
    std::uniform_int_distribution<int> disX(0, std::max(0, screenGeometry.width() - width()));
    std::uniform_int_distribution<int> disY(0, std::max(0, screenGeometry.height() - height()));

    this->move(disX(gen), disY(gen));
}

void MainWindow::on_dvdButton_toggled(bool checked)
{
    dvd = checked;
}

void MainWindow::checkPlatformSupport()
{
    QString platformName = QGuiApplication::platformName();
    
    if (platformName == "wayland") {
        canMoveWindow = false;
        QMessageBox::warning(this, "Platform Warning", 
            "Running on Wayland. Window positioning may not work properly.\n"
            "Consider running with: QT_QPA_PLATFORM=xcb ./ArrowKey\n"
            "or use X11 session for full functionality.");
    } else if (platformName == "xcb" || platformName == "x11") {
        canMoveWindow = true;
    } else {
        // 他のプラットフォームでは試してみる
        canMoveWindow = true;
    }
}
