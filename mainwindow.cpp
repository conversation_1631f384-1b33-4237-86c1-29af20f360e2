#include "mainwindow.h"
#include "ui_mainwindow.h"
#include <QTime>

using namespace std;

MainWindow::MainWindow(QWidget *parent) : QMainWindow(parent) , ui(new Ui::MainWindow)
{
    ui->setupUi(this);
    checkPlatformSupport();
    setupDVD();

    // 時刻表示用のラベルを作成
    timeLabel = new QLabel(this);
    timeLabel->setAlignment(Qt::AlignCenter);
    timeLabel->setStyleSheet("QLabel { font-size: 18px; font-weight: bold; }");
    setCentralWidget(timeLabel);

    // 時刻更新用タイマー
    displayTimer = new QTimer(this);
    connect(displayTimer, &QTimer::timeout, this, &MainWindow::updateTimeDisplay);
    displayTimer->start(1000); // 1秒ごとに更新
    updateTimeDisplay(); // 初期表示

    // DVDモード用タイマー
    QTimer *dvdTimer = new QTimer(this);
    dvdTimer->setSingleShot(false);
    dvdTimer->start(1); // 約60FPSで更新（16ms間隔）
    connect(dvdTimer, &QTimer::timeout, this, &MainWindow::moveDVD);
}

MainWindow::~MainWindow()
{
    delete ui;
}

void MainWindow::setupDVD()
{
   vert = UP;
   hor = LEFT;
   QRect screenGeometry = screen->geometry();
   // シード値を設定
   srand(static_cast<unsigned int>(time(nullptr)));
   iconX = rand() % (screenGeometry.width() - width()) + 1;
   iconY = rand() % (screenGeometry.height() - height()) + 1;
}

void MainWindow::updateTimeDisplay()
{
    QTime currentTime = QTime::currentTime();
    QString timeString = currentTime.toString("hh:mm:ss");
    timeLabel->setText("Nothing to do at " + timeString);
}

void MainWindow::moveDVD()
{
   if (!canMoveWindow) return;
   QRect screenGeometry = screen->geometry();

   // 境界判定を修正
   if (iconX <= 0 || iconX >= screenGeometry.width() - width()) {
       hor = (hor == LEFT) ? RIGHT : LEFT;
   }
   if (iconY <= 0 || iconY >= screenGeometry.height() - height()) {
       vert = (vert == UP) ? DOWN : UP;
   }

   // 位置を更新
   (vert == UP) ? iconY-- : iconY++;
   (hor == LEFT) ? iconX-- : iconX++;

   move(iconX, iconY);
}




void MainWindow::checkPlatformSupport()
{
    QString platformName = QGuiApplication::platformName();
    
    if (platformName == "wayland") {
        canMoveWindow = false;
        QMessageBox::warning(this, "Platform Warning", 
            "Running on Wayland. Window positioning may not work properly.\n"
            "Consider running with: QT_QPA_PLATFORM=xcb ./ArrowKey\n"
            "or use X11 session for full functionality.");
    } else if (platformName == "xcb" || platformName == "x11") {
        canMoveWindow = true;
    } else {
        // 他のプラットフォームでは試してみる
        canMoveWindow = true;
    }
}
